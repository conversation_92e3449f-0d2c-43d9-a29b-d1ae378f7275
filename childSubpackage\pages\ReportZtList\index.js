import {getReportPageListApi} from '../../../service/industryApi';
import {getShareUrl, handleShareUrl} from '../../../utils/mixin/pageShare';

const app = getApp();

// 常量配置
const CONSTANTS = {
  DEFAULT_HEIGHT: 600,
  MIN_HEIGHT: 400,
  TOAST_DURATION: 2000,
  NAVIGATE_DELAY: 1500,
  HEIGHT_CALC_DELAY: 100,

  // 排序选项
  SORT_OPTIONS: [
    {value: 'default', text: '默认排序'},
    {value: 'time', text: '时间排序'},
    {value: 'hot', text: '热度排序'}
  ],

  // 默认排序
  DEFAULT_SORT: {
    value: 'default',
    text: '默认排序'
  }
};

// 防抖函数
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func.apply(this, args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

Page({
  data: {
    // 专题信息
    topicId: '',
    topicTitle: '',
    topicSubtitle: '',

    // 报告列表相关
    reportList: [],
    totalCount: 0,

    // 排序相关数据
    showSortDropdown: false,
    currentSort: CONSTANTS.DEFAULT_SORT.value,
    currentSortText: CONSTANTS.DEFAULT_SORT.text,
    sortOptions: CONSTANTS.SORT_OPTIONS,

    // 高度相关
    listScrollHeight: CONSTANTS.DEFAULT_HEIGHT,

    // 用户状态
    isLogin: false,
    isVip: false,

    // 请求参数
    libraryRequestParams: {}
  },

  onLoad(options) {
    // 参数验证
    if (!this.validateParams(options)) return;

    // 初始化页面数据
    this.initPageData(options);
  },

  onReady() {
    this.calculateHeight();
  },

  onShow() {
    this.checkUserStatus();
    handleShareUrl();
  },

  /**
   * 验证页面参数
   */
  validateParams(options) {
    const {id} = options;
    if (!id) {
      wx.showToast({
        title: '专题ID不能为空',
        icon: 'none'
      });
      setTimeout(() => wx.navigateBack(), CONSTANTS.NAVIGATE_DELAY);
      return false;
    }
    return true;
  },

  /**
   * 初始化页面数据
   */
  initPageData(options) {
    const {id, title, subtitle} = options;
    const topicTitle = title ? decodeURIComponent(title) : '专题报告';

    // 一次性设置所有数据
    this.setData({
      topicId: id,
      topicTitle,
      topicSubtitle: subtitle ? decodeURIComponent(subtitle) : '0',
      libraryRequestParams: this.buildRequestParams(id)
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({title: topicTitle});

    // 检查用户状态
    this.checkUserStatus();
  },

  /**
   * 构建请求参数
   */
  buildRequestParams(topicId) {
    return {
      topic_id: topicId,
      sort: this.data.currentSort,
      type: 'hdzk',
      keyword: ''
    };
  },

  /**
   * 检查用户状态
   */
  checkUserStatus() {
    const isLogin = app.isLogin();
    const isVip = app.isVip ? app.isVip() : false;

    this.setData({isLogin, isVip});
  },

  /**
   * 计算列表高度（防抖处理）
   */
  calculateHeight: debounce(function () {
    try {
      wx.nextTick(() => {
        const query = wx.createSelectorQuery().in(this);

        // 获取AI入口高度
        query.select('.ai-entrance').boundingClientRect();
        // 获取统计信息容器高度
        query.select('.company_num_container').boundingClientRect();

        query.exec(res => {
          const {windowHeight} = wx.getSystemInfoSync();

          // 动态获取实际高度
          const statsHeight = res[1]?.height + res[1]?.top;

          // 计算可用高度
          const availableHeight = windowHeight - aiHeight - statsHeight + 40; // 40为安全边距

          this.setData({
            listScrollHeight: Math.max(availableHeight, CONSTANTS.MIN_HEIGHT)
          });
        });
      });
    } catch (error) {
      console.error('高度计算失败:', error);
      // 使用默认高度
      this.setData({
        listScrollHeight: CONSTANTS.DEFAULT_HEIGHT
      });
    }
  }, CONSTANTS.HEIGHT_CALC_DELAY),

  /**
   * AI入口点击
   */
  onAIEntranceClick() {
    if (!this.checkLoginStatus()) return;

    this.showAIComingSoonModal();
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    if (!this.data.isLogin) {
      app.route(this, '/pages/login/login');
      return false;
    }
    return true;
  },

  /**
   * 显示AI功能即将上线提示
   */
  showAIComingSoonModal() {
    wx.showModal({
      title: '提示',
      content: 'AI智能分析功能即将上线，将为您提供专题深度分析！',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 页面点击事件 - 关闭下拉框
   */
  onPageTap() {
    this.closeSortDropdown();
  },

  /**
   * 切换排序下拉框显示状态
   */
  onSortDropdownTap() {
    this.setData({
      showSortDropdown: !this.data.showSortDropdown
    });
  },

  /**
   * 排序选项容器点击 - 阻止冒泡
   */
  onSortOptionsContainerTap() {
    // catchtap 已阻止冒泡
  },

  /**
   * 选择排序选项
   */
  onSortOptionTap(e) {
    const {value, text} = e.currentTarget.dataset;

    // 如果选择相同选项，直接关闭
    if (value === this.data.currentSort) {
      this.closeSortDropdown();
      return;
    }

    // 更新排序并关闭下拉框
    this.updateSort(value, text);
  },

  /**
   * 关闭排序下拉框
   */
  closeSortDropdown() {
    if (this.data.showSortDropdown) {
      this.setData({showSortDropdown: false});
    }
  },

  /**
   * 更新排序方式
   */
  updateSort(value, text) {
    this.setData({
      currentSort: value,
      currentSortText: text,
      showSortDropdown: false,
      libraryRequestParams: {
        ...this.data.libraryRequestParams,
        sort: value
      }
    });
  },

  /**
   * 撼地智库数据变化回调
   */
  onLibraryDataChange(e) {
    const {list = [], total = 0} = e.detail;
    this.setData({
      reportList: list,
      totalCount: total
    });
  },

  /**
   * 撼地智库错误回调
   */
  onLibraryError(e) {
    console.error('撼地智库数据加载失败:', e.detail);
    this.showErrorToast('数据加载失败，请稍后重试');
  },

  /**
   * 报告点击回调
   */
  onLibraryReportClick(e) {
    const {item} = e.detail;
    console.log('报告点击:', item);
    // 可以在这里添加统计埋点等逻辑
  },

  /**
   * 显示错误提示
   */
  showErrorToast(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: CONSTANTS.TOAST_DURATION
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return this.buildShareConfig();
  },

  /**
   * 构建分享配置
   */
  buildShareConfig() {
    const {topicTitle, topicId, totalCount} = this.data;

    // 动态生成分享标题
    const title = this.generateShareTitle(topicTitle, totalCount);

    // 构建分享路径
    const path = this.buildSharePath(topicId, topicTitle);

    return {
      title,
      path,
      imageUrl:
        'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/report/r_ztbg.png'
    };
  },

  /**
   * 生成分享标题
   */
  generateShareTitle(topicTitle, totalCount) {
    return totalCount > 0
      ? `邀请你查看${totalCount}篇${topicTitle}相关报告`
      : `${topicTitle} - 专题报告`;
  },

  /**
   * 构建分享路径
   */
  buildSharePath(topicId, topicTitle) {
    const basePath = '/childSubpackage/pages/ReportZtList/index';
    const params = `id=${topicId}&title=${encodeURIComponent(topicTitle)}`;
    return getShareUrl(`${basePath}?${params}`);
  }
});

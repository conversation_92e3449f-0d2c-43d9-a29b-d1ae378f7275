<view class="pages" bindtap="onPageTap">
  <!-- 搜索输入框 -->
  <view style="background: white;padding: 20rpx 24rpx;">
    <view class="searchs">
      <view class="s-input">
        <view class="s-input-img">
          <image
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png"
            mode="aspectFit"
          />
        </view>
        <view class="s-input-item">
          <input
            class="s-input-item-i"
            type="text"
            placeholder="请输入关键字"
            placeholder-class="placeholder"
            bindfocus="onSearchFocus"
            bindblur="onSearchBlur"
            value="{{searchValue}}"
            focus="{{inputShowed}}"
            bindinput="onSearchInput"
            bindconfirm="onSearchConfirm"
            confirm-type="search"
          />
          <view
            hidden="{{searchValue.length <= 0}}"
            catchtap="onClear"
            class="input-clear"
          >
            <view class="clearIcon"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 搜索聚焦且无内容时显示历史记录和AI入口 -->
  <view class="history_wrap" wx:if="{{showSuggestions}}">
    <!-- 搜索历史 -->
    <block wx:if="{{searchHistory.length > 0}}">
      <view class="page__autofit search_a">
        <view class="his_title">
          <text class="his_title_l">历史记录</text>
          <view class="his_title_icon" bindtap="onClearHistory">
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/delet.png"
              mode="aspectFit"
            />
          </view>
        </view>
        <view class="his_content">
          <view class="text-box">
            <block wx:for="{{searchHistory}}" wx:key="index">
              <view
                class="his_content_item"
                bindtap="onSelectHistory"
                data-keyword="{{item}}"
              >
                {{item}}
              </view>
            </block>
          </view>
        </view>
      </view>
    </block>

    <!-- AI图片 -->
    <image
      class="ai_img"
      src="/image/report/r_nai_enter.png"
      bindtap="onEnterAI"
    />
  </view>

  <!-- 搜索状态下的结果头部 -->
  <view class="search-content" wx:if="{{showSearchResult}}">
    <view class="company_num">
      <view class="text_left">
        共找到<text class="color_num">{{searchResultCount}}</text
        >个相关专题
      </view>
      <view class="right-image" bindtap="onAskQuestion">
        <image src="/image/report/v_ww.png" mode="aspectFit" />
        <text>问一问</text>
      </view>
    </view>
  </view>

  <!-- 专题卡片列表 -->
  <view class="topic-list-container" wx:if="{{!showSuggestions}}">
    <scroll-view
      class="topic-scroll-view"
      style="height: {{listScrollHeight}}px;"
      scroll-y="true"
    >
      <view style="height: 20rpx;background-color:#F7F7F7;"></view>
      <view class="topic-list">
        <view
          class="topic-card"
          wx:for="{{topicList}}"
          wx:key="id"
          bindtap="onTopicTap"
          data-item="{{item}}"
        >
          <image src="/image/report/r_ztbg.png" class="topic_bg" />
          <view class="card-content">
            <view class="text-content">
              <view class="title">{{item.title}}</view>
              <view class="subtitle">共 {{item.subtitle}} 篇报告</view>
            </view>
            <view class="image-content">
              <image
                src="{{item.image || '/image/report/r_ztzw.png'}}"
                mode="aspectFit"
                lazy-load="true"
                show-menu-by-longpress="false"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view
        wx:if="{{topicList.length === 0 && !isLoading}}"
        class="empty-container"
      >
        <view class="empty-content">
          <image
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/null.png"
            class="empty-icon"
            mode="aspectFit"
          />
          <text
            class="empty-text"
            >{{isSearchMode ? '暂无相关专题' : '暂无专题数据'}}</text
          >
        </view>
      </view>

      <!-- 加载状态 -->
      <view
        wx:if="{{isLoading && topicList.length === 0}}"
        class="loading-container"
      >
        <view class="loading-content">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
      </view>

      <!-- 加载更多状态 -->
      <view
        wx:if="{{!isSearchMode && topicList.length > 0}}"
        class="load-more-container"
      >
        <view wx:if="{{isLoading}}" class="load-more-loading">
          <view class="loading-spinner-small"></view>
          <text>加载中...</text>
        </view>
        <view wx:elif="{{!hasMoreData}}" class="load-more-end">
          <text>没有更多数据了</text>
        </view>
      </view>
    </scroll-view>
  </view>
</view>

<view class="page">
  <!-- 第一行文字 居中 -->
  <view class="header-text">
    <text class="header-title">研报图表AI提取，无需阅读即可掌握精华</text>
  </view>

  <!-- 第二行 写死的 input输入框 点击跳转 参考thinkTankList -->
  <view class="search_container">
    <view class="searchs">
      <view class="s-input" bindtap="onSearchTap">
        <view class="s-input-img">
          <image
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png"
            mode="aspectFit"
          ></image>
        </view>
        <view class="s-input-item">
          <input
            class="s-input-item-i"
            type="text"
            placeholder="请输入关键词"
            placeholder-class="placeholder"
            disabled="{{true}}"
          />
        </view>
      </view>
    </view>
  </view>

  <!-- 调试信息 -->
  <view style="background: red; padding: 20rpx; color: white; margin: 10rpx;">
    调试：页面级别 - isVip: {{isVip}}, tbData长度: {{tbData.length}},
    chartList长度: {{chartList.length}}
  </view>

  <!-- 图表列表组件 - 包含研报入口和图表列表 -->
  <view class="chart-list-container">
    <ChartGridList
      chartList="{{chartList}}"
      containerHeight="{{listScrollHeight}}"
      isVip="{{isVip}}"
      showVipPage="{{true}}"
      showRemainingInfo="{{true}}"
      totalCount="{{chartList.length}}"
      bind:chartclick="onChartClick"
      bind:vippaysuccess="onVipPaySuccess"
    >
      <!-- 头部固定内容 -->
      <view slot="fixedCont" style="background: pink; padding: 10rpx;"
        >调试：fixedCont插槽 - 我是头部内容</view
      >

      <!-- 滚动区域内容 - 研报入口网格 -->
      <view slot="scrollContent">
        <view style="background: orange; padding: 10rpx; color: white;"
          >调试：scrollContent插槽内容</view
        >
        <view class="zt-container">
          <view style="background: cyan; padding: 5rpx;"
            >调试：zt-container，tbData长度：{{tbData.length}}</view
          >
          <view class="zt-grid">
            <view
              wx:for="{{tbData}}"
              wx:key="index"
              class="zt-item"
              bindtap="onTbClick"
              data-item="{{item}}"
            >
              <image
                class="zt-image"
                src="/image/report/{{item.code}}.png"
                mode="aspectFill"
                style="margin-bottom: 16rpx;"
              />
              <view class="zt-name">{{item.name}}</view>
            </view>
          </view>
        </view>
      </view>
    </ChartGridList>
  </view>
</view>

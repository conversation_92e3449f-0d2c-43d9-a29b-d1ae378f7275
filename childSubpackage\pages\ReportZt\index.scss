/* childSubpackage/pages/ReportZt/index.wxss */

/* 页面容器 */
.pages {
  height: 100vh;
  overflow: hidden;
  background: #f7f7f7;
}

/* 确保 hidden 元素不占用空间 */
[hidden] {
  display: none !important;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* 搜索输入框样式 */
.searchs {
  position: relative;
  z-index: 31;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #eeeeee;
  border-radius: 8rpx;
  padding: 16rpx 0 16rpx 28rpx;

  .s-input {
    display: flex;
    align-items: center;
    flex: 1;
  }
}

.s-input-img {
  width: 40rpx;
  height: 40rpx;
}

input {
  caret-color: #e72410;
  color: #74798c;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
}

.s-input-item {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  height: 40rpx;
  padding-left: 16rpx;
}

.s-input-item-i {
  position: relative;
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #05070c;
}

.placeholder {
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
  line-height: 40rpx;
}

.input-clear {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}

.clearIcon {
  width: 32rpx;
  height: 32rpx;
  background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMTZDMTIuNDE4MyAxNiAxNiAxMi40MTgzIDE2IDhDMTYgMy41ODE3IDEyLjQxODMgMCA4IDBDMy41ODE3IDAgMCAzLjU4MTcgMCA4QzAgMTIuNDE4MyAzLjU4MTcgMTYgOCAxNloiIGZpbGw9IiNDQ0NDQ0MiLz4KPHBhdGggZD0iTTEwLjUgNS41TDUuNSAxMC41IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxwYXRoIGQ9Ik01LjUgNS41TDEwLjUgMTAuNSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K")
    no-repeat center;
  background-size: contain;
}

/* 历史记录和搜索建议样式 */
.history_wrap {
  background-color: #f7f7f7;
  padding-top: 20rpx;
}

.page__autofit {
  background-color: #fff;
  padding: 28rpx 0;
}

.page__autofit:nth-child(1) {
  margin-top: 0;
}

.his_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx 24rpx;
  border-bottom: 1px solid #eeeeee;
}

.his_title_l {
  font-weight: 400;
  font-size: 28rpx;
  color: #20263a;
}

.his_title_icon {
  width: 36rpx;
  height: 36rpx;
}

.his_content {
  display: flex;
  width: 100%;
  overflow: hidden;
  max-height: 160rpx;
  padding: 0 32rpx;
}

.text-box {
  text-align: justify;
  display: flex;
  flex-wrap: wrap;
}

.his_content_item {
  background: #f5f6f7;
  border-radius: 8rpx;
  margin-top: 20rpx;
  margin-right: 20rpx;
  padding: 8rpx 20rpx;
  min-width: 96rpx;
  max-width: 680rpx;
  height: 56rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  justify-content: center;
  text-align: center;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  color: #74798c;
}

/* AI图片样式 */
.ai_img {
  width: 750rpx;
  height: 184rpx;
  margin: 20rpx 0;
}

/* 搜索结果样式 */
.search-content {
  .company_num {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 28rpx 32rpx;
    background: #fff;
    margin-top: 20rpx;

    .text_left {
      font-size: 28rpx;
      font-weight: 400;
      color: #9b9eac;
    }

    .color_num {
      color: #e72410;
      padding: 0 4rpx;
    }

    .right-image {
      display: flex;
      align-items: center;
      image {
        width: 40rpx;
        height: 40rpx;
        margin-right: 8rpx;
      }
      text {
        font-weight: 400;
        font-size: 28rpx;
        color: #74798c;
      }
    }
  }
}

/* 专题列表容器 */
.topic-list-container {
  flex: 1;
}

.topic-scroll-view {
  width: 100%;
  background-color: #fff;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  width: 100%;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #74798c;
  text-align: center;
}

/* 专题列表 */
.topic-list {
  padding: 24rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 22rpx;
}

.topic-card {
  position: relative;
  width: 340rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

  .topic_bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

.card-content {
  width: 340rpx;
  height: 160rpx;
  padding: 20rpx 16rpx 20rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

.text-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-right: 12rpx;
  height: 100%;

  .title {
    font-weight: 600;
    font-size: 28rpx;
    color: #20263a;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 32rpx;
      left: 0rpx;
      width: 72rpx;
      height: 8rpx;
      background: linear-gradient(90deg, #ff7e7e 6%, rgba(255, 126, 126, 0.1) 100%);
    }
  }
  .subtitle {
    font-weight: 400;
    font-size: 24rpx;
    color: #9b9eac;
    text-align: left;
  }
}

.image-content {
  width: 112rpx;
  height: 112rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-content image {
  width: 112rpx;
  height: 112rpx;
  border-radius: 10rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  width: 100%;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #e72410;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #74798c;
  text-align: center;
}

/* 加载更多 */
.load-more-container {
  text-align: center;
  padding: 40rpx 0;
}

.load-more-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #74798c;
  font-size: 26rpx;
}

.loading-spinner-small {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #f3f3f3;
  border-top: 2rpx solid #e72410;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

.load-more-end {
  color: #999;
  font-size: 26rpx;
}

/* 旋转动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .topic-card {
    width: 100%;
  }

  .card-content {
    width: 100%;
  }
}

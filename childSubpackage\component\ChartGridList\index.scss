// 图表网格容器样式
.chart-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  gap: 0 18rpx;
}

// 剩余信息提示样式
.remaining-info {
  padding: 32rpx 24rpx;
  text-align: center;
  background-color: #ffffff;
  position: relative;

  // 横线样式
  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 24rpx;
    right: 24rpx;
    height: 1rpx;
    background-color: #eee;
  }

  .remaining-text {
    position: relative;
    padding: 0 24rpx;
    font-size: 26rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #74798c;
    display: inline-block;
    background: #fff;
    text {
      font-weight: 400;
      font-size: 28rpx;
      color: #e72410;
      padding: 0 4rpx;
    }
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 120rpx 0;

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

// 图片预览蒙层样式
.image-preview-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;

  .image-preview-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .preview-image {
      max-width: 100%;
      max-height: 80vh;
      border-radius: 8rpx;
    }

    .close-btn {
      position: absolute;
      top: -60rpx;
      right: -60rpx;
      width: 80rpx;
      height: 80rpx;
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      .close-icon {
        font-size: 48rpx;
        color: #333;
        font-weight: bold;
        line-height: 1;
      }
    }
  }
}

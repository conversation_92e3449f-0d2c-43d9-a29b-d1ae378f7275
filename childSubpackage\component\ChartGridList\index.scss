// 重新构造的卡片样式
.chart-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
}

.chart-card {
  background: #f7f7f7;

  // 上面是图片
  .chart-image-container {
    width: 342rpx;
    height: 200rpx;

    .chart-image {
      width: 100%;
      height: 100%;
    }
  }

  // 下面是文字
  .chart-content {
    padding: 10rpx 16rpx 6rpx;

    .chart-title {
      display: block;
      font-weight: 400;
      font-size: 28rpx;
      color: #20263a;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  // 最下面：左边日期，右边固定文字12346
  .chart-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16rpx 10rpx;

    .chart-date {
      font-weight: 400;
      font-size: 22rpx;
      color: #9b9eac;
    }

    .chart-number {
      font-weight: 400;
      font-size: 22rpx;
      color: #fd9331;
    }
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 120rpx 0;

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

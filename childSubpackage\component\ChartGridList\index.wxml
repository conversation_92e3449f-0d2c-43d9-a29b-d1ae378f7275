<!-- 图表网格列表组件 - 重新构造卡片样式 -->
<view class="chart-grid-list">
  <!-- 组件调试信息 -->
  <view style="background: green; padding: 10rpx; color: white; margin: 5rpx;">
    调试：ChartGridList组件已渲染 - isVip: {{isVip}}, chartList长度:
    {{chartList.length}}
  </view>

  <!-- 固定内容插槽 - 在所有情况下都显示 -->
  <slot></slot>
  <slot name="fixedCont"></slot>

  <!-- 列表页面 -->
  <view class="list-container">
    <!-- VIP用户：显示完整列表 -->
    <view wx:if="{{isVip}}" class="vip-container">
      <view class="card-box" style="height: {{containerHeight}}px;">
        <scroll-view
          class="chart-scroll"
          scroll-y="{{true}}"
          style="height: {{containerHeight}}px;"
          enhanced="{{true}}"
          bounces="{{false}}"
        >
          <!-- VIP用户的滚动区域内容插槽 -->
          <slot name="scrollContent"></slot>
          <view class="chart-grid">
            <!-- 使用card组件 - VIP用户显示2个卡片 -->
            <chart-card
              wx:for="{{chartList.slice(0, 2)}}"
              wx:key="id"
              item="{{item}}"
              bind:cardclick="onChartClick"
              bind:imagepreview="onImagePreview"
            />
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 非VIP用户：显示2条数据 + VIP页面 -->
    <scroll-view
      wx:else
      class="non-vip-container"
      scroll-y="{{true}}"
      style="height: {{containerHeight}}px;"
      enhanced="{{true}}"
      bounces="{{false}}"
    >
      <!-- 滚动区域内的插槽 - 用于放置zt-container等内容 -->
      <slot name="scrollContent"></slot>
      <!-- 显示第一条图表数据 -->
      <view wx:if="{{chartList.length > 0}}" class="first-chart-container">
        <!-- 使用card组件 -->
        <view class="chart-grid">
          <chart-card
            item="{{chartList[0]}}"
            bind:cardclick="onChartClick"
            bind:imagepreview="onImagePreview"
          />
          <chart-card
            item="{{chartList[1]}}"
            bind:cardclick="onChartClick"
            bind:imagepreview="onImagePreview"
          />
        </view>
      </view>

      <!-- 剩余信息提示 -->
      <view wx:if="{{showVipPage && showRemainingInfo}}" class="remaining-info">
        <view class="remaining-text">
          剩余<text>{{totalCount > 2 ? totalCount - 2 : 0}}</text
          >条相关信息
        </view>
      </view>

      <!-- VIP购买页面 -->
      <vip-page wx:if="{{showVipPage}}" bind:paySuccess="onVipPaySuccess">
      </vip-page>
      <view style="height: 60rpx;"></view>
    </scroll-view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{chartList.length === 0}}" class="empty-state">
    <text class="empty-text">暂无图表数据</text>
  </view>

  <!-- 图片预览蒙层 -->
  <view
    wx:if="{{showImagePreview}}"
    class="image-preview-mask"
    bindtap="onMaskTap"
  >
    <view class="image-preview-container" bindtap="stopPropagation">
      <image class="preview-image" src="{{previewImageUrl}}" mode="aspectFit" />
      <view class="close-btn" bindtap="closeImagePreview">
        <text class="close-icon">×</text>
      </view>
    </view>
  </view>
</view>

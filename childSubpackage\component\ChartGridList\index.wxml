<!-- 图表网格列表组件 - 重新构造卡片样式 -->
<view class="chart-grid-list">
  <!-- 列表页面 -->
  <view class="list-container">
    <!-- VIP用户：显示完整列表 -->
    <view wx:if="{{isVip}}" class="vip-container">
      <view class="card-box" style="height: {{containerHeight}}px;">
        <scroll-view
          class="chart-scroll"
          scroll-y="{{true}}"
          style="height: {{containerHeight}}px;"
          enhanced="{{true}}"
          bounces="{{false}}"
        >
          <view class="chart-grid">
            <!-- 重新构造的卡片 -->
            <view
              wx:for="{{chartList}}"
              wx:key="id"
              class="chart-card"
              bindtap="onChartClick"
              data-item="{{item}}"
            >
              <!-- 上面是图片 -->
              <view class="chart-image-container">
                <image
                  class="chart-image"
                  src="{{item.imgUrl}}"
                  mode="aspectFill"
                />
              </view>

              <!-- 下面是文字 -->
              <view class="chart-content">
                <text class="chart-title">{{item.title}}</text>
              </view>

              <!-- 最下面：左边日期，右边固定文字12346 -->
              <view class="chart-footer">
                <text class="chart-date">{{item.publishTime}}</text>
                <text class="chart-number">12346</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 非VIP用户：显示一条数据 + VIP页面 -->
    <scroll-view
      wx:else
      class="non-vip-container"
      scroll-y="{{true}}"
      style="height: {{containerHeight}}px;"
      enhanced="{{true}}"
      bounces="{{false}}"
    >
      <slot name="fixedCont"></slot>
      <!-- 显示第一条图表数据 -->
      <view wx:if="{{chartList.length > 0}}" class="first-chart-container">
        <!-- 重新构造的单个卡片 -->
        <view class="chart-grid">
          <view
            class="chart-card"
            bindtap="onChartClick"
            data-item="{{chartList[0]}}"
          >
            <!-- 上面是图片 -->
            <view class="chart-image-container">
              <image
                class="chart-image"
                src="{{chartList[0].imgUrl}}"
                mode="aspectFill"
              />
            </view>

            <!-- 下面是文字 -->
            <view class="chart-content">
              <text class="chart-title">{{chartList[0].title}}</text>
            </view>

            <!-- 最下面：左边日期，右边固定文字12346 -->
            <view class="chart-footer">
              <text class="chart-date">{{chartList[0].publishTime}}</text>
              <text class="chart-number">东吴证券</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 剩余信息提示 -->
      <view wx:if="{{showVipPage && showRemainingInfo}}" class="remaining-info">
        <view class="remaining-text">
          剩余<text>{{totalCount > 1 ? totalCount - 1 : 0}}</text
          >条图表信息
        </view>
      </view>

      <!-- VIP购买页面 -->
      <vip-page wx:if="{{showVipPage}}" bind:paySuccess="onVipPaySuccess">
      </vip-page>
      <view style="height: 60rpx;"></view>
    </scroll-view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{chartList.length === 0}}" class="empty-state">
    <text class="empty-text">暂无图表数据</text>
  </view>
</view>

.page {
  min-height: 100vh;
  background: #f7f7f7;
}

// 第一行文字 居中
.header-text {
  text-align: center;
  background: #fff;

  .header-title {
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 22rpx;
    color: #9b9eac;
    line-height: 36rpx;
  }
}

// 第二行 搜索框 完全借鉴thinkTankList
/* input */
.search_container {
  background: #fff;
  padding: 20rpx 24rpx 8rpx;

  .searchs {
    position: relative;
    z-index: 31;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #eeeeee;
    border-radius: 8rpx;
    padding: 16rpx 0 16rpx 28rpx;
  }

  .s-input {
    display: flex;
    align-items: center;
    flex: 1;
    cursor: pointer;
  }

  .s-input-img {
    width: 36rpx;
    height: 36rpx;
  }
  .s-input-item {
    display: flex;
    align-items: center;
    position: relative;
    flex: 1;
    height: 40rpx;
    padding-left: 16rpx;
  }

  .s-input-item-i {
    position: relative;
    flex: 1;
    height: 40rpx;
    font-size: 28rpx;
    font-family:
      <PERSON>Fang SC,
      PingFang SC-Regular;
    font-weight: 400;
  }

  .placeholder {
    font-size: 28rpx;
    color: #9b9eac;
  }
}

// 第三行 研报入口 直接搬运pages/report/report的zt-container样式
.zt-container {
  padding: 0 24rpx 40rpx;
  background: #fff;
  .zt-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr); // 一行4个
    grid-template-rows: repeat(2, 1fr); // 一共2行

    .zt-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 32rpx;

      .zt-image {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 16rpx;
        overflow: hidden;
      }

      .zt-name {
        font-weight: 400;
        font-size: 24rpx;
        color: #20263a;
        line-height: 36rpx;
        text-align: center;
      }
    }
  }
}

// 第四行 图表列表容器
.chart-list-container {
  background: #fff;
}

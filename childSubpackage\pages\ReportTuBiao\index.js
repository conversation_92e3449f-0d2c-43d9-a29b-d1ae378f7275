const app = getApp();

// 常量配置
const CONSTANTS = {
  DEFAULT_HEIGHT: 600,
  MIN_HEIGHT: 400,
  HEIGHT_CALC_DELAY: 100
};

// 防抖函数
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func.apply(this, args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

Page({
  data: {
    // 图表列表数据
    chartList: [],
    listScrollHeight: CONSTANTS.DEFAULT_HEIGHT,

    // 图表入口数据（直接搬运pages/report/report的tbData）
    tbData: [
      {
        name: '产业概述',
        code: 'cygs'
      },
      {
        name: '市场规模',
        code: 'scgm'
      },
      {
        name: '竞争格局',
        code: 'jjjl'
      },
      {
        name: '产业链图谱',
        code: 'cyltp'
      },
      {
        name: '政策法规',
        code: 'zcfg'
      },
      {
        name: '技术创新',
        code: 'jscx'
      },
      {
        name: '行业数据',
        code: 'hysj'
      },
      {
        name: '企业分析',
        code: 'qyfx'
      }
    ],

    // 用户状态
    isLogin: false,
    isVip: false
  },

  onLoad(options) {
    this.initData();
  },

  onReady() {
    this.calculateHeight();
  },

  onShow() {
    this.checkUserStatus();
  },

  /**
   * 初始化数据
   */
  initData() {
    this.checkUserStatus();
    this.loadChartData();
  },

  /**
   * 检查用户状态
   */
  checkUserStatus() {
    const isLogin = app.isLogin();
    const isVip = app.isVip ? app.isVip() : false;
    this.setData({isLogin, isVip});
  },

  /**
   * 加载图表数据（模拟数据，使用占位图）
   */
  loadChartData() {
    // 模拟图表数据，使用占位图替换
    const mockChartList = [
      {
        id: 1,
        title: '2024年市场趋势分析',
        imgUrl:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/report/r_ztbg.png',
        type: 'line',
        summary: '详细分析2024年市场发展趋势',
        publishTime: '2024-01-15'
      },
      {
        id: 2,
        title: '行业对比数据分析',
        imgUrl:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/report/r_ztbg.png',
        type: 'bar',
        summary: '多维度行业数据对比分析',
        publishTime: '2024-01-14'
      },
      {
        id: 3,
        title: '市场份额分布图表',
        imgUrl:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/report/r_ztbg.png',
        type: 'pie',
        summary: '各行业市场份额占比分析',
        publishTime: '2024-01-13'
      },
      {
        id: 4,
        title: '增长率散点分析',
        imgUrl:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/report/r_ztbg.png',
        type: 'scatter',
        summary: '企业增长率相关性分析',
        publishTime: '2024-01-12'
      },
      {
        id: 5,
        title: '销售额变化趋势',
        imgUrl:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/report/r_ztbg.png',
        type: 'line',
        summary: '年度销售额变化趋势分析',
        publishTime: '2024-01-11'
      },
      {
        id: 6,
        title: '用户增长对比报告',
        imgUrl:
          'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/report/r_ztbg.png',
        type: 'bar',
        summary: '用户增长数据对比分析',
        publishTime: '2024-01-10'
      }
    ];

    this.setData({
      chartList: mockChartList
    });
  },

  /**
   * 计算列表高度（防抖处理）
   */
  calculateHeight: debounce(function () {
    try {
      wx.nextTick(() => {
        const query = wx.createSelectorQuery().in(this);

        // 获取各个容器的高度
        query.select('.header-text').boundingClientRect();
        query.select('.searchs').boundingClientRect();
        query.select('.zt-container').boundingClientRect();

        query.exec(res => {
          const {windowHeight} = wx.getSystemInfoSync();

          // 动态获取实际高度
          const headerHeight = res[0]?.height || 0;
          const searchHeight = res[1]?.height || 0;
          const ztHeight = res[2]?.height + res[2]?.top || 0;

          // 计算可用高度
          const availableHeight = windowHeight - ztHeight; // 40为安全边距
          this.setData({
            listScrollHeight: availableHeight
          });
        });
      });
    } catch (error) {
      // 使用默认高度
      this.setData({
        listScrollHeight: CONSTANTS.DEFAULT_HEIGHT
      });
    }
  }, CONSTANTS.HEIGHT_CALC_DELAY),

  /**
   * 搜索框点击事件
   */
  onSearchTap() {
    // 跳转到搜索页面，参考thinkTankList的实现
    wx.navigateTo({
      url: '/childSubpackage/pages/searchPage/index?type=chart'
    });
  },

  /**
   * 图表入口点击事件（直接搬运pages/report/report的onTbClick）
   */
  onTbClick(e) {
    const {item} = e.currentTarget.dataset;
    // const url = `/childSubpackage/pages/tbReport/index?name=${item.name}&code=${item.code}`;
    // app.route(this, url);

    // 临时提示，后续可以取消注释上面的跳转逻辑
    wx.showModal({
      title: '图表分析',
      content: `即将查看${item.name}相关内容`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 图表点击事件
   */
  onChartClick(e) {
    const {item} = e.detail;

    if (!this.data.isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }

    wx.showModal({
      title: '查看图表',
      content: `即将查看图表：${item.title}`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * VIP支付成功回调
   */
  onVipPaySuccess() {
    // 重新检查用户状态
    this.checkUserStatus();

    wx.showToast({
      title: 'VIP开通成功',
      icon: 'success',
      duration: 2000
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: '研报图表 - 数据可视化分析',
      path: '/childSubpackage/pages/ReportTuBiao/index',
      imageUrl:
        'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/report/r_ztbg.png'
    };
  }
});

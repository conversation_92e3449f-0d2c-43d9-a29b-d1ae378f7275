Component({
  properties: {
    chartList: {
      type: Array,
      value: []
    },
    containerHeight: {
      type: Number,
      value: 400
    },
    isVip: {
      type: Boolean,
      value: false
    },
    showVipPage: {
      type: Boolean,
      value: true
    },
    showRemainingInfo: {
      type: Boolean,
      value: true
    },
    totalCount: {
      type: Number,
      value: 0
    }
  },

  data: {},

  methods: {
    /**
     * 图表点击事件
     */
    onChartClick(e) {
      const item = e.detail ? e.detail.item : e.currentTarget.dataset.item;
      this.triggerEvent('chartclick', {item});
    },

    /**
     * VIP支付成功回调
     */
    onVipPaySuccess() {
      this.triggerEvent('vippaysuccess');
    }
  }
});

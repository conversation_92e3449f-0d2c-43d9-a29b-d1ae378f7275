Component({
  properties: {
    chartList: {
      type: Array,
      value: []
    },
    containerHeight: {
      type: Number,
      value: 400
    },
    isVip: {
      type: Boolean,
      value: false
    },
    showVipPage: {
      type: Boolean,
      value: true
    },
    showRemainingInfo: {
      type: Boolean,
      value: true
    },
    totalCount: {
      type: Number,
      value: 0
    }
  },

  data: {
    // 图片预览相关
    showImagePreview: false,
    previewImageUrl: ''
  },

  lifetimes: {
    attached() {
      // 如果没有传入数据，使用mock数据
      if (
        !this.properties.chartList ||
        this.properties.chartList.length === 0
      ) {
        this.setData({
          chartList: this.getMockData(),
          totalCount: this.getMockData().length
        });
      }
    }
  },

  methods: {
    /**
     * 获取mock数据
     */
    getMockData() {
      return [
        {
          id: 1,
          title: '2024年新能源汽车行业深度分析报告',
          imgUrl: 'https://picsum.photos/342/200?random=1',
          publishTime: '2024-01-15',
          footerText: '东吴证券'
        },
        {
          id: 2,
          title: '人工智能技术发展趋势与投资机会',
          imgUrl: 'https://picsum.photos/342/200?random=2',
          publishTime: '2024-01-14',
          footerText: '12346'
        },
        {
          id: 3,
          title: '医疗健康产业链投资价值分析',
          imgUrl: 'https://picsum.photos/342/200?random=3',
          publishTime: '2024-01-13',
          footerText: '华泰证券'
        },
        {
          id: 4,
          title: '5G通信技术商业化应用前景',
          imgUrl: 'https://picsum.photos/342/200?random=4',
          publishTime: '2024-01-12',
          footerText: '中信证券'
        },
        {
          id: 5,
          title: '绿色金融发展现状与未来展望',
          imgUrl: 'https://picsum.photos/342/200?random=5',
          publishTime: '2024-01-11',
          footerText: '招商证券'
        }
      ];
    },

    /**
     * 图表点击事件 - 处理来自card组件的事件
     */
    onChartClick(e) {
      const item = e.detail.item;
      this.triggerEvent('chartclick', {item});
    },

    /**
     * 图片预览事件 - 处理来自card组件的图片点击
     */
    onImagePreview(e) {
      const imageUrl = e.detail.imageUrl;
      this.setData({
        showImagePreview: true,
        previewImageUrl: imageUrl
      });
    },

    /**
     * 关闭图片预览
     */
    closeImagePreview() {
      this.setData({
        showImagePreview: false,
        previewImageUrl: ''
      });
    },

    /**
     * 点击蒙层关闭预览
     */
    onMaskTap() {
      this.closeImagePreview();
    },

    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
      // 阻止点击图片时触发蒙层点击事件
    },

    /**
     * VIP支付成功回调
     */
    onVipPaySuccess() {
      this.triggerEvent('vippaysuccess');
    }
  }
});

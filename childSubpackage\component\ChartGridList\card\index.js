// childSubpackage/component/ChartGridList/card/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 图表数据项
    item: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 卡片点击事件
     */
    onCardClick() {
      this.triggerEvent('cardclick', {
        item: this.data.item
      });
    },

    /**
     * 图片点击事件
     */
    onImageClick() {
      // 使用catch:tap已经阻止了事件冒泡
      this.triggerEvent('imagepreview', {
        imageUrl: this.data.item.imgUrl
      });
    }
  }
});

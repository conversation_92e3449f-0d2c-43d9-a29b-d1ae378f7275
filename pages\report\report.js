import {getReportPageListApi} from '../../service/industryApi';
import {hasPrivile} from '../../utils/route';

const app = getApp();

Page({
  data: {
    isLogin: app.isLogin(), // 是否登录
    // 研报相关数据
    reportList: [],
    reportPage: 1,
    reportPageSize: 10,
    reportTotal: 0,
    reportHasMore: true,
    reportLoading: false,
    showReportModule: true, // 是否显示撼地智库模块
    isVip: false, // 是否为VIP用户
    showVipPage: false, // 是否显示VIP页面
    ztData: [
      // 动态获取
      {
        name: '生物医药',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      },
      {
        name: '智能网联新能源汽车专题',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      },
      {
        name: '低空经济',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      },
      {
        name: '人形机器人',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      },
      {
        name: '智能网联新能源汽车专题',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      },
      {
        name: '生物医药',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      },
      {
        name: '生物医药',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      },
      {
        name: '生物医药',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      }
    ],
    tbData: [
      {
        name: '产业概述',
        code: 'cygs'
      },
      {
        name: '市场规模',
        code: 'scgm'
      },
      {
        name: '竞争格局',
        code: 'jjjl'
      },
      {
        name: '产业链图谱',
        code: 'cyltp'
      },
      {
        name: '政策法规',
        code: 'zcfg'
      },
      {
        name: '技术创新',
        code: 'jscx'
      },
      {
        name: '行业数据',
        code: 'hysj'
      },
      {
        name: '企业分析',
        code: 'qyfx'
      }
    ]
  },

  onLoad: async function () {
    // 检查VIP状态
    await this.checkVipStatus();

    // 初始化时获取研报数据
    if (this.data.isLogin) {
      this.getReportList(true);
    }
  },

  onShow: function () {
    this.setData({
      isLogin: app.isLogin()
    });
    // 如果登录状态改变，重新获取数据
    if (this.data.isLogin && this.data.reportList.length === 0) {
      this.getReportList(true);
    }
  },

  // 检查VIP状态
  async checkVipStatus() {
    // this.setData({
    //   isVip: false,
    //   showVipPage: true
    // });
    // return;
    if (!this.data.isLogin) {
      this.setData({
        isVip: false,
        showVipPage: false
      });
      return;
    }

    try {
      const vipStatus = await hasPrivile({
        packageType: true
      });

      const isVip = vipStatus !== '游客' && vipStatus !== '普通VIP';

      this.setData({
        isVip,
        showVipPage: !isVip
      });
    } catch (error) {
      console.error('检查VIP状态失败:', error);
      this.setData({
        isVip: false,
        showVipPage: true
      });
    }
  },

  // 获取研报列表
  async getReportList(isRefresh = false) {
    if (this.data.reportLoading) return;

    this.setData({
      reportLoading: true
    });

    try {
      const page = isRefresh ? 1 : this.data.reportPage;

      // 使用项目中的API调用方式
      const response = await getReportPageListApi({
        type: 'chainMap', // 产业图谱类型
        page_index: page,
        page_size: this.data.reportPageSize
      });

      if (response) {
        const newList = response.list || [];
        const total = response.total || 0;

        // 转换数据格式
        const transformedNewList = this.transformReportData(newList);

        let reportList;
        if (isRefresh) {
          reportList = transformedNewList;
        } else {
          reportList = [...this.data.reportList, ...transformedNewList];
        }

        // 如果不是VIP用户，只显示一条数据
        if (!this.data.isVip && reportList.length > 0) {
          reportList = reportList.slice(0, 1);
        }

        // 判断是否还有更多数据
        let hasMore = reportList.length < total;

        // 如果不是VIP用户，不显示查看更多
        if (!this.data.isVip) {
          hasMore = false;
        }

        // 判断是否显示撼地智库模块
        const showReportModule = total > 0;

        this.setData({
          reportList,
          reportTotal: total,
          reportPage: page + 1,
          reportHasMore: hasMore,
          showReportModule
        });
      } else {
        // 接口返回错误
        if (isRefresh) {
          this.setData({
            reportList: [],
            showReportModule: false
          });
        }
        wx.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取研报列表失败:', error);
      if (isRefresh) {
        this.setData({
          reportList: [],
          showReportModule: false
        });
      }
      wx.showToast({
        title: '网络错误，请稍后重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        reportLoading: false
      });
    }
  },

  // 转换API数据格式
  transformReportData(apiList) {
    if (!Array.isArray(apiList)) {
      return [];
    }

    return apiList.map(item => {
      // 处理产业链标签
      const tags = [];
      if (item.chains && Array.isArray(item.chains)) {
        // 取前2个产业链作为标签
        tags.push(...item.chains.slice(0, 2).map(chain => chain.name));
      }

      return {
        id: item.id,
        title: item.report_name || '--',
        size: item.file_size || '-',
        tags: tags,
        organization: item.publish_org || '未知机构',
        date: this.formatDate(item.publish_time),
        pdfUrl: item.report_oss_url || '',
        imgTit: item.report_type === 'REPORT_TYPE_1' ? '撼地智库' : '产业专题',
        page_num: item.page_num,
        // 保留原始数据以备后用
        originalData: item
      };
    });
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return '--';
    try {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      return '--';
    }
  },

  // 处理跳转点击事件
  handleClick(e) {
    const {isLogin} = this.data;
    const constant = e.currentTarget.dataset?.type;

    if (!isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }

    let url = '';
    switch (constant) {
      case 'search':
        url = '/childSubpackage/pages/ResearchThreeList/index?type=report';
        break;
      case 'ai':
        // url = '/childSubpackage/pages/aiReport/index';
        break;
      case 'zt':
        // url = '/childSubpackage/pages/ztReport/index';
        break;
      case 'tb':
        // url = '/childSubpackage/pages/tbReport/index';
        break;
      case 'zk':
        // url = '/childSubpackage/pages/zkReport/index';
        break;
      default:
        break;
    }

    if (url) {
      app.route(this, url);
    }
  },
  // 点击更多
  onMoreClick(e) {
    let type = e.currentTarget.dataset.type,
      url = '';

    console.log('onMoreClick 被触发，type:', type);

    switch (type) {
      case 'zk': // 智库更多
        url = '/childSubpackage/pages/thinkTankList/index';
        break;
      case 'zt': // 专题更多
        url = '/childSubpackage/pages/ReportZt/index';
        break;
      case 'tb': // 图表更多
        url = '/childSubpackage/pages/ReportTuBiao/index';
        console.log('图表更多被点击，准备跳转到:', url);
        break;
      default:
        console.log('未知的type:', type);
        break;
    }

    if (url) {
      console.log('执行跳转到:', url);
      app.route(this, url);
    } else {
      console.log('url为空，无法跳转');
    }
  },

  // 加载更多研报
  onLoadMore() {
    if (!this.data.reportHasMore || this.data.reportLoading) {
      return;
    }
    this.getReportList(false);
  },

  // 下拉刷新
  onPullDownRefresh() {
    // 非VIP用户禁止下拉刷新
    if (!this.data.isVip) {
      return;
    }
    this.getReportList(true);
  },

  // VIP支付成功回调
  onVipPaySuccess() {
    // 重新检查VIP状态
    this.checkVipStatus().then(() => {
      // 如果成为VIP，重新获取数据
      if (this.data.isVip) {
        this.getReportList(true);
      }
    });
  },
  // 点击产业专题
  onZtClick(e) {
    const {item} = e.currentTarget.dataset;
    // const url = `/industryPackage/pages/IndustryListMasonry/index?chain_name=${item.name}&chain_code=${item.code}`;
    // app.route(this, url);
  },
  // 点击研报图表
  onTbClick(e) {
    const {item} = e.currentTarget.dataset;
    // const url = `/childSubpackage/pages/tbReport/index?name=${item.name}&code=${item.code}`;
    // app.route(this, url);
  },
  // 点击撼地智库
  onItemClick(e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.reportList[index];
    const isvip = this.data.isVip;
    if (!isvip) {
      app.showToast('请购买VIP后查看', 'none', 2000, false);
      return;
    }
    // 显示操作选择弹窗
    this.showReportActionSheet(item);
  },

  // 显示研报操作选择
  showReportActionSheet(reportItem) {
    const itemList = ['在线预览', '下载到本地'];

    wx.showActionSheet({
      itemList,
      success: res => {
        switch (res.tapIndex) {
          case 0:
            this.previewReport(reportItem);
            break;
          case 1:
            this.downloadReport(reportItem);
            break;
        }
      },
      fail: res => {
        console.log('用户取消操作');
      }
    });
  },

  // 在线预览研报
  previewReport(reportItem) {
    wx.showLoading({
      title: '正在加载...'
    });

    // 获取PDF URL
    const pdfUrl = reportItem.pdfUrl || reportItem.report_oss_url || '';

    if (!pdfUrl) {
      wx.hideLoading();
      wx.showToast({
        title: '暂无预览文件',
        icon: 'none'
      });
      return;
    }

    // 使用微信内置PDF预览
    wx.downloadFile({
      url: pdfUrl,
      success: res => {
        wx.hideLoading();
        if (res.statusCode === 200) {
          wx.openDocument({
            filePath: res.tempFilePath,
            fileType: 'pdf',
            success: () => {
              console.log('PDF预览成功');
            },
            fail: error => {
              console.error('PDF预览失败:', error);
              wx.showToast({
                title: '预览失败，请稍后重试',
                icon: 'none'
              });
            }
          });
        }
      },
      fail: error => {
        wx.hideLoading();
        console.error('下载PDF失败:', error);
        wx.showToast({
          title: '加载失败，请检查网络',
          icon: 'none'
        });
      }
    });
  },

  // 下载研报到本地
  downloadReport(reportItem) {
    wx.showLoading({
      title: '正在下载...'
    });

    // 获取PDF URL
    const pdfUrl = reportItem.pdfUrl || reportItem.report_oss_url || '';

    if (!pdfUrl) {
      wx.hideLoading();
      wx.showToast({
        title: '暂无下载文件',
        icon: 'none'
      });
      return;
    }

    wx.downloadFile({
      url: pdfUrl,
      success: res => {
        wx.hideLoading();
        if (res.statusCode === 200) {
          // 保存到相册或文件管理器
          wx.saveFile({
            tempFilePath: res.tempFilePath,
            success: saveRes => {
              wx.showToast({
                title: '下载成功',
                icon: 'success'
              });
              console.log('文件保存路径:', saveRes.savedFilePath);
            },
            fail: error => {
              console.error('保存文件失败:', error);
              wx.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          });
        }
      },
      fail: error => {
        wx.hideLoading();
        console.error('下载失败:', error);
        wx.showToast({
          title: '下载失败，请稍后重试',
          icon: 'none'
        });
      }
    });
  }
});

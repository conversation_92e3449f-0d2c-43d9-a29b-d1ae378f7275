import {
  getTopicPageListApi,
  searchTopicApi
} from '../../../service/industryApi';
import {getHeight} from '../../../utils/height';

const app = getApp();

Page({
  data: {
    searchValue: '', // 搜索输入值
    searchHistory: [], // 搜索历史
    showSuggestions: false, // 是否显示搜索建议
    showSearchResult: false, // 是否显示搜索结果
    searchResultCount: 0, // 搜索结果数量
    topicList: [], // 专题列表
    inputShowed: false, // 输入框是否聚焦

    listScrollHeight: 600, // 列表滚动区域高度

    // 分页相关
    currentPage: 1,
    pageSize: 10,
    hasMoreData: true,
    isLoading: false,

    // 搜索状态
    isSearchMode: false, // 是否处于搜索模式
    searchKeyword: '' // 当前搜索关键词
  },
  onLoad(options) {
    this.initData();
    this.loadSearchHistory();
  },

  onReady() {
    // 页面渲染完成后计算高度
    this.initPageHeight();
  },

  onShow() {
    // 页面显示时重新计算高度（可能从其他页面返回）
    this.calculateListHeight();
  },

  onHide() {
    // 页面隐藏时清除定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
  },

  onUnload() {
    // 页面卸载时清除定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
  },

  // 初始化页面高度
  initPageHeight() {
    this.calculateListHeight();
  },

  // 动态计算列表高度
  calculateListHeight() {
    setTimeout(() => {
      if (this.data.showSearchResult) {
        // 搜索状态下，需要考虑搜索结果头部
        getHeight(this, ['.searchs', '.search-content'], data => {
          const {screeHeight, res} = data;
          const searchHeight = res[0]?.height || 80;
          const searchResultHeight = res[1]?.height || 0;
          const listHeight =
            screeHeight - searchHeight - searchResultHeight - 40; // 40为padding

          this.setData({
            listScrollHeight: Math.max(listHeight, 300)
          });
        });
      } else {
        // 普通状态下，只需要考虑搜索框
        getHeight(this, ['.searchs'], data => {
          const {screeHeight, res} = data;
          const searchHeight = res[0]?.height || 80;
          const listHeight = screeHeight - searchHeight - 40; // 40为padding

          this.setData({
            listScrollHeight: Math.max(listHeight, 300)
          });
        });
      }
    }, 100);
  },

  // 初始化数据
  async initData() {
    await this.loadTopicList(true);
  },

  // 加载专题列表
  async loadTopicList(isRefresh = false) {
    if (this.data.isLoading) return;

    const page = isRefresh ? 1 : this.data.currentPage;

    this.setData({
      isLoading: true
    });

    try {
      const response = await getTopicPageListApi({
        page_index: page,
        page_size: this.data.pageSize
      });

      if (response && response.code === 200 && response.data) {
        const {list = [], total = 0} = response.data;
        const transformedData = this.transformTopicData(list);

        if (isRefresh) {
          // 刷新数据
          this.setData({
            topicList: transformedData,
            currentPage: 1,
            searchResultCount: total,
            hasMoreData: total > this.data.pageSize
          });
        } else {
          // 加载更多
          this.setData({
            topicList: [...this.data.topicList, ...transformedData],
            currentPage: page,
            hasMoreData:
              this.data.topicList.length + transformedData.length < total
          });
        }
      } else {
        // API调用失败，使用模拟数据
        console.warn('API调用失败，使用模拟数据');
        this.loadMockData(isRefresh);
      }
    } catch (error) {
      console.error('加载专题列表失败:', error);
      // 出错时使用模拟数据
      this.loadMockData(isRefresh);
    } finally {
      this.setData({
        isLoading: false
      });
    }
  },

  // 加载模拟数据（作为备用方案）
  loadMockData(isRefresh = false) {
    const allTopics = this.generateAllMockTopics();

    if (isRefresh) {
      this.setData({
        topicList: allTopics,
        searchResultCount: allTopics.length,
        currentPage: 1,
        hasMoreData: false
      });
    }
  },

  // 转换API数据格式
  transformTopicData(apiList) {
    if (!Array.isArray(apiList)) {
      return [];
    }

    return apiList.map(item => ({
      id: item.id,
      title: item.topic_name || item.title || '未知专题',
      subtitle: item.report_count || item.subtitle || '0',
      image: item.topic_image || '/image/report/r_ztzw.png',
      description: item.description || '',
      // 保留原始数据
      originalData: item
    }));
  },

  // 加载搜索历史
  loadSearchHistory() {
    try {
      let history = wx.getStorageSync('search_history_report') || [];

      // 如果没有历史记录，添加一些mock数据
      if (history.length === 0) {
        history = [
          '新能源汽车',
          '人工智能',
          '5G通信',
          '区块链技术',
          '数字化转型'
        ];
        // 保存mock数据到本地存储
        wx.setStorageSync('search_history_report', history);
      }

      this.setData({searchHistory: history});
    } catch (e) {
      console.error('加载搜索历史失败:', e);
      // 如果读取失败，使用默认mock数据
      this.setData({
        searchHistory: [
          '新能源汽车',
          '人工智能',
          '5G通信',
          '区块链技术',
          '数字化转型'
        ]
      });
    }
  },

  // 保存搜索历史
  saveSearchHistory(keyword) {
    if (!keyword.trim()) return;

    try {
      let history = wx.getStorageSync('search_history_report') || [];

      // 移除重复项
      history = history.filter(item => item !== keyword);

      // 添加到开头
      history.unshift(keyword);

      // 限制历史记录数量
      if (history.length > 10) {
        history = history.slice(0, 10);
      }

      wx.setStorageSync('search_history_report', history);
      this.setData({searchHistory: history});
    } catch (e) {
      console.error('保存搜索历史失败:', e);
    }
  },

  // 搜索输入事件
  onSearchInput(e) {
    const value = e.detail.value;
    this.setData({
      searchValue: value,
      showSearchResult: value.trim().length > 0,
      showSuggestions: false
    });

    // 防抖处理，避免频繁调用搜索接口
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }

    this.searchTimer = setTimeout(() => {
      if (value.trim().length > 0) {
        this.performSearch(value);
      } else {
        // 清空搜索，重新加载所有数据
        this.resetToInitialState();
      }
    }, 500); // 500ms防抖
  },

  // 搜索框聚焦事件
  onSearchFocus() {
    if (!this.data.searchValue.trim()) {
      this.setData({showSuggestions: true});
    }
  },

  // 搜索框失焦事件
  onSearchBlur() {
    // 延迟隐藏，避免点击历史记录时立即隐藏
    setTimeout(() => {
      this.setData({
        showSuggestions: false,
        inputShowed: false
      });
    }, 200);
  },

  // 清空输入框
  onClear() {
    this.resetToInitialState();
  },

  // 清空历史记录
  onClearHistory() {
    wx.showModal({
      title: '提示',
      content: '确定要清空搜索记录吗？',
      success: res => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('search_history_report');
            this.setData({searchHistory: []});
            wx.showToast({
              title: '已清空',
              icon: 'success'
            });
          } catch (e) {
            console.error('清空搜索历史失败:', e);
          }
        }
      }
    });
  },

  // 页面点击事件
  onPageTap() {
    // 可以在这里处理页面点击事件
  },

  // 搜索确认事件
  onSearchConfirm() {
    const keyword = this.data.searchValue.trim();
    if (!keyword) return;

    this.saveSearchHistory(keyword);
    this.performSearch(keyword);
    this.setData({
      showSuggestions: false,
      showSearchResult: true
    });
  },

  // 执行搜索
  async performSearch(keyword) {
    if (!keyword.trim()) {
      // 如果搜索关键词为空，重新加载所有数据
      this.setData({
        isSearchMode: false,
        searchKeyword: ''
      });
      await this.loadTopicList(true);
      return;
    }

    this.setData({
      isSearchMode: true,
      searchKeyword: keyword,
      isLoading: true
    });

    try {
      const response = await searchTopicApi({
        keyword: keyword,
        page_index: 1,
        page_size: 50 // 搜索时加载更多数据
      });

      if (response && response.code === 200 && response.data) {
        const {list = [], total = 0} = response.data;
        const transformedData = this.transformTopicData(list);

        this.setData({
          topicList: transformedData,
          searchResultCount: total,
          currentPage: 1,
          hasMoreData: false // 搜索模式下暂不支持分页
        });
      } else {
        // API搜索失败，使用本地搜索作为备用
        console.warn('API搜索失败，使用本地搜索');
        this.performLocalSearch(keyword);
      }
    } catch (error) {
      console.error('搜索失败:', error);
      // 出错时使用本地搜索
      this.performLocalSearch(keyword);
    } finally {
      this.setData({
        isLoading: false
      });
      this.calculateListHeight();
    }
  },

  // 本地搜索（备用方案）
  performLocalSearch(keyword) {
    const allTopics = this.generateAllMockTopics();
    const regex = new RegExp(keyword, 'i');
    const filteredTopics = allTopics.filter(topic => regex.test(topic.title));

    this.setData({
      topicList: filteredTopics,
      searchResultCount: filteredTopics.length,
      currentPage: 1,
      hasMoreData: false
    });
  },

  // 生成所有模拟数据用于搜索
  generateAllMockTopics() {
    const topics = [
      {
        id: 1,
        title: '新能源汽车产业发展趋势',
        subtitle: '50'
      },
      {
        id: 2,
        title: '人工智能技术应用',
        subtitle: '50'
      },
      {
        id: 3,
        title: '5G通信技术革命',
        subtitle: '50'
      },
      {
        id: 4,
        title: '绿色环保产业链',
        subtitle: '50'
      },
      {
        id: 5,
        title: '数字化转型趋势',
        subtitle: '50'
      },
      {
        id: 6,
        title: '生物医药创新发展',
        subtitle: '50'
      },
      {
        id: 7,
        title: '智能制造产业升级',
        subtitle: '50'
      },
      {
        id: 8,
        title: '区块链技术应用',
        subtitle: '50'
      },
      {
        id: 9,
        title: '云计算服务发展',
        subtitle: '50'
      },
      {
        id: 10,
        title: '物联网产业生态',
        subtitle: '50'
      },
      {
        id: 11,
        title: '物联网产业生态',
        subtitle: '50'
      },
      {
        id: 12,
        title: '物联网产业生态',
        subtitle: '50'
      },
      {
        id: 13,
        title: '物联网产业生态',
        subtitle: '50'
      },
      {
        id: 14,
        title: '物联网产业生态',
        subtitle: '50'
      },
      {
        id: 15,
        title: '物联网产业生态',
        subtitle: '50'
      },
      {
        id: 16,
        title: '物联网产业生态',
        subtitle: '50'
      },
      {
        id: 17,
        title: '物联网产业生态',
        subtitle: '50'
      }
    ];

    return topics.map(topic => ({
      ...topic,
      image: '/image/report/r_ztzw.png'
    }));
  },

  // 选择历史记录
  onSelectHistory(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({
      searchValue: keyword,
      showSuggestions: false,
      showSearchResult: true
    });
    this.performSearch(keyword);
  },

  // 进入AI分析
  onEnterAI() {
    // 检查用户登录状态
    if (!app.isLogin()) {
      app.route(this, '/pages/login/login');
      return;
    }

    wx.showModal({
      title: '提示',
      content: 'AI智能分析功能即将上线，敬请期待！',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 问一问功能
  onAskQuestion() {
    // 检查用户登录状态
    if (!app.isLogin()) {
      app.route(this, '/pages/login/login');
      return;
    }

    wx.showModal({
      title: '提示',
      content: '智能问答功能即将上线，您可以通过AI助手快速获取专题相关信息！',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 点击专题卡片
  onTopicTap(e) {
    const item = e.currentTarget.dataset.item;

    if (!item || !item.id) {
      wx.showToast({
        title: '专题信息错误',
        icon: 'none'
      });
      return;
    }

    // 检查用户登录状态
    if (!app.isLogin()) {
      app.route(this, '/pages/login/login');
      return;
    }

    // 跳转到专题报告列表页面
    wx.navigateTo({
      url: `/childSubpackage/pages/ReportZtList/index?id=${
        item.id
      }&title=${encodeURIComponent(item.title)}&subtitle=${encodeURIComponent(
        item.subtitle
      )}`
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadTopicList(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom() {
    if (
      !this.data.isSearchMode &&
      this.data.hasMoreData &&
      !this.data.isLoading
    ) {
      this.loadTopicList(false);
    }
  },

  // 清空搜索，重新加载数据
  async resetToInitialState() {
    this.setData({
      searchValue: '',
      showSearchResult: false,
      showSuggestions: false,
      isSearchMode: false,
      searchKeyword: ''
    });
    await this.loadTopicList(true);
    this.calculateListHeight();
  }
});
